<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                💡 Lifestyle & Money-Saving Blog
            </h1>
            <p class="text-xl text-blue-100 max-w-2xl mx-auto">
                Discover expert tips, product reviews, and lifestyle guides to help you save money and live better.
            </p>
        </div>
    </div>
</section>

<!-- Search & Filters -->
<section class="py-8 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
            <!-- Search -->
            <div class="flex-1 max-w-md">
                <form action="<?php echo e(route('blog.index')); ?>" method="GET" class="relative">
                    <input type="text"
                           name="search"
                           placeholder="Search articles..."
                           value="<?php echo e(request('search')); ?>"
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="hidden" name="category" value="<?php echo e(request('category')); ?>">
                </form>
            </div>

            <!-- Category Filter -->
            <div class="flex items-center space-x-4">
                <select name="category"
                        onchange="window.location.href = '<?php echo e(route('blog.index')); ?>?category=' + this.value + '&search=<?php echo e(request('search')); ?>'"
                        class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Categories</option>
                    <?php $__currentLoopData = $categories ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($category->slug); ?>" <?php echo e(request('category') === $category->slug ? 'selected' : ''); ?>>
                            <?php echo e($category->name); ?> (<?php echo e($category->blogs_count); ?>)
                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
    </div>
</section>

<!-- Featured Articles -->
<?php if(isset($featuredBlogs) && $featuredBlogs->count() > 0): ?>
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">⭐ Featured Articles</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <?php $__currentLoopData = $featuredBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('components.blog-card', ['blog' => $blog], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Main Content -->
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Articles Grid -->
            <div class="flex-1">
                <?php if(request('search') || request('category')): ?>
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">
                            <?php if(request('search')): ?>
                                Search Results for "<?php echo e(request('search')); ?>"
                            <?php elseif(request('category')): ?>
                                <?php echo e($categories->where('slug', request('category'))->first()->name ?? 'Category'); ?> Articles
                            <?php endif; ?>
                        </h2>
                        <p class="text-gray-600 mt-1"><?php echo e($blogs->total()); ?> <?php echo e(Str::plural('article', $blogs->total())); ?> found</p>
                    </div>
                <?php else: ?>
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">📚 Latest Articles</h2>
                <?php endif; ?>

                <?php $__empty_1 = true; $__currentLoopData = $blogs ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="mb-8">
                        <?php echo $__env->make('components.blog-card', ['blog' => $blog], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <!-- Demo Content -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <article class="card-hover">
                            <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 rounded-t-xl flex items-center justify-center">
                                <span class="text-white text-4xl font-bold">💰</span>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center space-x-2 mb-3">
                                    <span class="badge-primary">Money Saving</span>
                                    <span class="text-sm text-gray-500">Dec 15, 2024</span>
                                </div>
                                <h3 class="font-bold text-xl text-gray-900 mb-2">10 Smart Ways to Save Money on Online Shopping</h3>
                                <p class="text-gray-600 mb-4">Discover proven strategies to maximize your savings when shopping online. From cashback apps to price comparison tools, learn how to never pay full price again.</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-medium">J</span>
                                        </div>
                                        <span class="text-sm text-gray-500">John Doe • 5 min read</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </article>

                        <article class="card-hover">
                            <div class="w-full h-48 bg-gradient-to-br from-green-400 to-blue-500 rounded-t-xl flex items-center justify-center">
                                <span class="text-white text-4xl font-bold">🛍️</span>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center space-x-2 mb-3">
                                    <span class="badge-success">Shopping Guide</span>
                                    <span class="text-sm text-gray-500">Dec 12, 2024</span>
                                </div>
                                <h3 class="font-bold text-xl text-gray-900 mb-2">Best Black Friday Deals 2024: Complete Guide</h3>
                                <p class="text-gray-600 mb-4">Don't miss out on the biggest shopping event of the year. Here are the best deals and how to find them before they sell out.</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-medium">S</span>
                                        </div>
                                        <span class="text-sm text-gray-500">Sarah Smith • 8 min read</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </article>

                        <article class="card-hover">
                            <div class="w-full h-48 bg-gradient-to-br from-purple-400 to-pink-500 rounded-t-xl flex items-center justify-center">
                                <span class="text-white text-4xl font-bold">🏠</span>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center space-x-2 mb-3">
                                    <span class="badge-warning">Lifestyle</span>
                                    <span class="text-sm text-gray-500">Dec 10, 2024</span>
                                </div>
                                <h3 class="font-bold text-xl text-gray-900 mb-2">Transform Your Home on a Budget: 15 DIY Ideas</h3>
                                <p class="text-gray-600 mb-4">Create a beautiful living space without breaking the bank. These budget-friendly DIY projects will inspire your next home makeover.</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-medium">M</span>
                                        </div>
                                        <span class="text-sm text-gray-500">Mike Johnson • 6 min read</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </article>

                        <article class="card-hover">
                            <div class="w-full h-48 bg-gradient-to-br from-red-400 to-orange-500 rounded-t-xl flex items-center justify-center">
                                <span class="text-white text-4xl font-bold">🍳</span>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center space-x-2 mb-3">
                                    <span class="badge-danger">Food & Dining</span>
                                    <span class="text-sm text-gray-500">Dec 8, 2024</span>
                                </div>
                                <h3 class="font-bold text-xl text-gray-900 mb-2">Meal Prep on a Budget: Save Time and Money</h3>
                                <p class="text-gray-600 mb-4">Learn how to prepare delicious, healthy meals for the entire week without spending a fortune. Includes recipes and shopping tips.</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-medium">E</span>
                                        </div>
                                        <span class="text-sm text-gray-500">Emily Chen • 7 min read</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </article>
                    </div>
                <?php endif; ?>

                <!-- Pagination -->
                <?php if(isset($blogs) && $blogs->hasPages()): ?>
                    <div class="mt-8">
                        <?php echo e($blogs->appends(request()->query())->links()); ?>

                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="lg:w-80">
                <!-- Popular Categories -->
                <div class="card mb-6">
                    <div class="p-6">
                        <h3 class="font-bold text-lg text-gray-900 mb-4">📂 Popular Categories</h3>
                        <div class="space-y-2">
                            <?php $__empty_1 = true; $__currentLoopData = $categories ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <a href="<?php echo e(route('blog.index', ['category' => $category->slug])); ?>"
                                   class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                    <span class="text-gray-700"><?php echo e($category->name); ?></span>
                                    <span class="badge-gray"><?php echo e($category->blogs_count); ?></span>
                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <a href="#" class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                                    <span class="text-gray-700">Money Saving</span>
                                    <span class="badge-gray">12</span>
                                </a>
                                <a href="#" class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                                    <span class="text-gray-700">Shopping Guide</span>
                                    <span class="badge-gray">8</span>
                                </a>
                                <a href="#" class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                                    <span class="text-gray-700">Lifestyle</span>
                                    <span class="badge-gray">15</span>
                                </a>
                                <a href="#" class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                                    <span class="text-gray-700">Food & Dining</span>
                                    <span class="badge-gray">6</span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Newsletter Signup -->
                <div class="card bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
                    <div class="p-6 text-center">
                        <div class="text-4xl mb-3">📧</div>
                        <h3 class="font-bold text-lg text-gray-900 mb-2">Stay Updated</h3>
                        <p class="text-gray-600 text-sm mb-4">Get the latest articles and money-saving tips delivered to your inbox.</p>
                        <form class="space-y-3">
                            <input type="email"
                                   placeholder="Your email address"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button type="submit" class="w-full btn-primary">
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Talha\blog-app\resources\views/blog/index.blade.php ENDPATH**/ ?>