<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- SEO Meta Tags -->
    <title>{{ $seoData['title'] ?? config('app.name', 'DealHub') }}</title>
    <meta name="description" content="{{ $seoData['description'] ?? 'Discover the best deals, exclusive coupons, and lifestyle content. Save money with verified coupon codes and deals from top brands.' }}">
    @if(isset($seoData['keywords']))
        <meta name="keywords" content="{{ is_array($seoData['keywords']) ? implode(', ', $seoData['keywords']) : $seoData['keywords'] }}">
    @endif
    <meta name="robots" content="{{ $seoData['robots'] ?? 'index,follow' }}">
    <link rel="canonical" href="{{ $seoData['canonical'] ?? url()->current() }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ $seoData['title'] ?? config('app.name', 'DealHub') }}">
    <meta property="og:description" content="{{ $seoData['description'] ?? 'Discover the best deals, exclusive coupons, and lifestyle content.' }}">
    <meta property="og:type" content="{{ $seoData['type'] ?? 'website' }}">
    <meta property="og:url" content="{{ $seoData['canonical'] ?? url()->current() }}">
    @if(isset($seoData['image']))
        <meta property="og:image" content="{{ $seoData['image'] }}">
    @endif
    <meta property="og:site_name" content="{{ config('app.name', 'DealHub') }}">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $seoData['title'] ?? config('app.name', 'DealHub') }}">
    <meta name="twitter:description" content="{{ $seoData['description'] ?? 'Discover the best deals, exclusive coupons, and lifestyle content.' }}">
    @if(isset($seoData['image']))
        <meta name="twitter:image" content="{{ $seoData['image'] }}">
    @endif

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional head content -->
    @stack('head')
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Navigation -->
    @include('layouts.navigation')

    <!-- Main Content -->
    <main class="min-h-screen">
        @yield('content')
    </main>

    <!-- Footer -->
    @include('layouts.footer')

    <!-- Scripts -->
    @stack('scripts')
</body>
</html>