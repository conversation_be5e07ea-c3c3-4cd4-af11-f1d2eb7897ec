<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Spatie\ResponseCache\Facades\ResponseCache;

class BlogController extends Controller
{
    /**
     * Show all blogs (paginated).
     */
    public function index()
    {
        $page = request('page', 1);

        $blogs = Cache::rememberForever('blogs_page_' . $page, function () {
            return Blog::latest()->paginate(5);
        });

        return view('blogs.index', compact('blogs'));
    }

    /**
     * Show a single blog post.
     */
    public function show($slug)
    {
        $blog = Cache::rememberForever('blog_' . $slug, function () use ($slug) {
            return Blog::where('slug', $slug)->firstOrFail();
        });

        return view('blogs.show', compact('blog'));
    }

    /**
     * Store a new blog post.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title'   => 'required|string|max:255',
            'content' => 'required|string',
        ]);

        $slug = str_slug($request->title);

        $blog = Blog::create([
            'title'   => $request->title,
            'slug'    => $slug,
            'content' => $request->content,
        ]);

        // Only invalidate the new blog and page 1
        Cache::forget('blogs_page_1');
        Cache::forget('blog_' . $slug);

        ResponseCache::clear();

        return redirect()->route('blog.index')->with('success', 'Blog created successfully');
    }

    public function update(Request $request, Blog $blog)
    {
        $request->validate([
            'title'   => 'required|string|max:255',
            'content' => 'required|string',
        ]);

        $oldSlug = $blog->slug;

        $blog->update([
            'title'   => $request->title,
            'slug'    => str_slug($request->title),
            'content' => $request->content,
        ]);

        // Only clear old + new blog slug
        Cache::forget('blog_' . $oldSlug);
        Cache::forget('blog_' . $blog->slug);

        ResponseCache::clear();

        return redirect()->route('blog.show', $blog->slug)->with('success', 'Blog updated successfully');
    }

    public function destroy(Blog $blog)
    {
        $slug = $blog->slug;
        $blog->delete();

        // Only clear that blog’s cache
        Cache::forget('blog_' . $slug);

        ResponseCache::clear();

        return redirect()->route('blog.index')->with('success', 'Blog deleted successfully');
    }
}
