<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\CouponController;

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'contactSubmit'])->name('contact.submit');

// Hidden promotions page (accessible only via direct link)
Route::get('/promotions', [HomeController::class, 'promotions'])->name('promotions');

// Blog routes
Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/category/{slug}', [BlogController::class, 'category'])->name('blog.category');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');

// Category routes
Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
Route::get('/categories/{slug}', [CategoryController::class, 'show'])->name('categories.show');
Route::get('/categories/{slug}/ajax', [CategoryController::class, 'ajax'])->name('categories.ajax');

// Store routes
Route::get('/stores', [StoreController::class, 'index'])->name('stores.index');
Route::get('/stores/{slug}', [StoreController::class, 'show'])->name('stores.show');
Route::get('/stores/{slug}/visit', [StoreController::class, 'visit'])->name('stores.visit');

// Coupon routes
Route::get('/coupons', [CouponController::class, 'index'])->name('coupons.index');
Route::get('/coupons/{slug}', [CouponController::class, 'show'])->name('coupons.show');
Route::get('/coupons/{slug}/visit', [CouponController::class, 'visit'])->name('coupons.visit');
Route::post('/coupons/{slug}/success', [CouponController::class, 'markSuccess'])->name('coupons.success');
Route::post('/coupons/{slug}/code', [CouponController::class, 'getCode'])->name('coupons.code');

// Search route
Route::get('/search', function () {
    return redirect()->route('coupons.index', ['search' => request('q')]);
})->name('search');

// Admin Routes
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    use App\Http\Controllers\Admin\AdminController;
    use App\Http\Controllers\Admin\BlogController as AdminBlogController;
    use App\Http\Controllers\Admin\CategoryController as AdminCategoryController;
    use App\Http\Controllers\Admin\StoreController as AdminStoreController;
    use App\Http\Controllers\Admin\CouponController as AdminCouponController;

    // Dashboard
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/analytics', [AdminController::class, 'analytics'])->name('analytics');
    Route::get('/system-info', [AdminController::class, 'systemInfo'])->name('system-info');
    Route::post('/clear-cache', [AdminController::class, 'clearCache'])->name('clear-cache');

    // Blog Management
    Route::resource('blogs', AdminBlogController::class);
    Route::post('blogs/{blog}/toggle-published', [AdminBlogController::class, 'togglePublished'])->name('blogs.toggle-published');
    Route::post('blogs/{blog}/toggle-featured', [AdminBlogController::class, 'toggleFeatured'])->name('blogs.toggle-featured');

    // Category Management
    Route::resource('categories', AdminCategoryController::class);
    Route::post('categories/{category}/toggle-active', [AdminCategoryController::class, 'toggleActive'])->name('categories.toggle-active');
    Route::post('categories/update-order', [AdminCategoryController::class, 'updateOrder'])->name('categories.update-order');

    // Store Management
    Route::resource('stores', AdminStoreController::class);
    Route::post('stores/{store}/toggle-active', [AdminStoreController::class, 'toggleActive'])->name('stores.toggle-active');
    Route::post('stores/{store}/toggle-featured', [AdminStoreController::class, 'toggleFeatured'])->name('stores.toggle-featured');

    // Coupon Management
    Route::resource('coupons', AdminCouponController::class);
    Route::post('coupons/{coupon}/toggle-active', [AdminCouponController::class, 'toggleActive'])->name('coupons.toggle-active');
    Route::post('coupons/{coupon}/toggle-featured', [AdminCouponController::class, 'toggleFeatured'])->name('coupons.toggle-featured');
    Route::post('coupons/{coupon}/toggle-verified', [AdminCouponController::class, 'toggleVerified'])->name('coupons.toggle-verified');
    Route::post('coupons/bulk-update-expiration', [AdminCouponController::class, 'bulkUpdateExpiration'])->name('coupons.bulk-update-expiration');
});
