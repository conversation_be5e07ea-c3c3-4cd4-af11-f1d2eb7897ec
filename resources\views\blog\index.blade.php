@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                💡 Lifestyle & Money-Saving Blog
            </h1>
            <p class="text-xl text-blue-100 max-w-2xl mx-auto">
                Discover expert tips, product reviews, and lifestyle guides to help you save money and live better.
            </p>
        </div>
    </div>
</section>

<!-- Search & Filters -->
<section class="py-8 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
            <!-- Search -->
            <div class="flex-1 max-w-md">
                <form action="{{ route('blog.index') }}" method="GET" class="relative">
                    <input type="text"
                           name="search"
                           placeholder="Search articles..."
                           value="{{ request('search') }}"
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="hidden" name="category" value="{{ request('category') }}">
                </form>
            </div>

            <!-- Category Filter -->
            <div class="flex items-center space-x-4">
                <select name="category"
                        onchange="window.location.href = '{{ route('blog.index') }}?category=' + this.value + '&search={{ request('search') }}'"
                        class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Categories</option>
                    @foreach($categories ?? [] as $category)
                        <option value="{{ $category->slug }}" {{ request('category') === $category->slug ? 'selected' : '' }}>
                            {{ $category->name }} ({{ $category->blogs_count }})
                        </option>
                    @endforeach
                </select>
            </div>
        </div>
    </div>
</section>

<!-- Featured Articles -->
@if(isset($featuredBlogs) && $featuredBlogs->count() > 0)
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">⭐ Featured Articles</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            @foreach($featuredBlogs as $blog)
                @include('components.blog-card', ['blog' => $blog])
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Main Content -->
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Articles Grid -->
            <div class="flex-1">
                @if(request('search') || request('category'))
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">
                            @if(request('search'))
                                Search Results for "{{ request('search') }}"
                            @elseif(request('category'))
                                {{ $categories->where('slug', request('category'))->first()->name ?? 'Category' }} Articles
                            @endif
                        </h2>
                        <p class="text-gray-600 mt-1">{{ $blogs->total() }} {{ Str::plural('article', $blogs->total()) }} found</p>
                    </div>
                @else
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">📚 Latest Articles</h2>
                @endif

                @forelse($blogs ?? [] as $blog)
                    <div class="mb-8">
                        @include('components.blog-card', ['blog' => $blog])
                    </div>
                @empty
                    <!-- Demo Content -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <article class="card-hover">
                            <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 rounded-t-xl flex items-center justify-center">
                                <span class="text-white text-4xl font-bold">💰</span>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center space-x-2 mb-3">
                                    <span class="badge-primary">Money Saving</span>
                                    <span class="text-sm text-gray-500">Dec 15, 2024</span>
                                </div>
                                <h3 class="font-bold text-xl text-gray-900 mb-2">10 Smart Ways to Save Money on Online Shopping</h3>
                                <p class="text-gray-600 mb-4">Discover proven strategies to maximize your savings when shopping online. From cashback apps to price comparison tools, learn how to never pay full price again.</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-medium">J</span>
                                        </div>
                                        <span class="text-sm text-gray-500">John Doe • 5 min read</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </article>

                        <article class="card-hover">
                            <div class="w-full h-48 bg-gradient-to-br from-green-400 to-blue-500 rounded-t-xl flex items-center justify-center">
                                <span class="text-white text-4xl font-bold">🛍️</span>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center space-x-2 mb-3">
                                    <span class="badge-success">Shopping Guide</span>
                                    <span class="text-sm text-gray-500">Dec 12, 2024</span>
                                </div>
                                <h3 class="font-bold text-xl text-gray-900 mb-2">Best Black Friday Deals 2024: Complete Guide</h3>
                                <p class="text-gray-600 mb-4">Don't miss out on the biggest shopping event of the year. Here are the best deals and how to find them before they sell out.</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-medium">S</span>
                                        </div>
                                        <span class="text-sm text-gray-500">Sarah Smith • 8 min read</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </article>

                        <article class="card-hover">
                            <div class="w-full h-48 bg-gradient-to-br from-purple-400 to-pink-500 rounded-t-xl flex items-center justify-center">
                                <span class="text-white text-4xl font-bold">🏠</span>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center space-x-2 mb-3">
                                    <span class="badge-warning">Lifestyle</span>
                                    <span class="text-sm text-gray-500">Dec 10, 2024</span>
                                </div>
                                <h3 class="font-bold text-xl text-gray-900 mb-2">Transform Your Home on a Budget: 15 DIY Ideas</h3>
                                <p class="text-gray-600 mb-4">Create a beautiful living space without breaking the bank. These budget-friendly DIY projects will inspire your next home makeover.</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-medium">M</span>
                                        </div>
                                        <span class="text-sm text-gray-500">Mike Johnson • 6 min read</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </article>

                        <article class="card-hover">
                            <div class="w-full h-48 bg-gradient-to-br from-red-400 to-orange-500 rounded-t-xl flex items-center justify-center">
                                <span class="text-white text-4xl font-bold">🍳</span>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center space-x-2 mb-3">
                                    <span class="badge-danger">Food & Dining</span>
                                    <span class="text-sm text-gray-500">Dec 8, 2024</span>
                                </div>
                                <h3 class="font-bold text-xl text-gray-900 mb-2">Meal Prep on a Budget: Save Time and Money</h3>
                                <p class="text-gray-600 mb-4">Learn how to prepare delicious, healthy meals for the entire week without spending a fortune. Includes recipes and shopping tips.</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-medium">E</span>
                                        </div>
                                        <span class="text-sm text-gray-500">Emily Chen • 7 min read</span>
                                    </div>
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </article>
                    </div>
                @endforelse

                <!-- Pagination -->
                @if(isset($blogs) && $blogs->hasPages())
                    <div class="mt-8">
                        {{ $blogs->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:w-80">
                <!-- Popular Categories -->
                <div class="card mb-6">
                    <div class="p-6">
                        <h3 class="font-bold text-lg text-gray-900 mb-4">📂 Popular Categories</h3>
                        <div class="space-y-2">
                            @forelse($categories ?? [] as $category)
                                <a href="{{ route('blog.index', ['category' => $category->slug]) }}"
                                   class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                    <span class="text-gray-700">{{ $category->name }}</span>
                                    <span class="badge-gray">{{ $category->blogs_count }}</span>
                                </a>
                            @empty
                                <a href="#" class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                                    <span class="text-gray-700">Money Saving</span>
                                    <span class="badge-gray">12</span>
                                </a>
                                <a href="#" class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                                    <span class="text-gray-700">Shopping Guide</span>
                                    <span class="badge-gray">8</span>
                                </a>
                                <a href="#" class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                                    <span class="text-gray-700">Lifestyle</span>
                                    <span class="badge-gray">15</span>
                                </a>
                                <a href="#" class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                                    <span class="text-gray-700">Food & Dining</span>
                                    <span class="badge-gray">6</span>
                                </a>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Newsletter Signup -->
                <div class="card bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
                    <div class="p-6 text-center">
                        <div class="text-4xl mb-3">📧</div>
                        <h3 class="font-bold text-lg text-gray-900 mb-2">Stay Updated</h3>
                        <p class="text-gray-600 text-sm mb-4">Get the latest articles and money-saving tips delivered to your inbox.</p>
                        <form class="space-y-3">
                            <input type="email"
                                   placeholder="Your email address"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button type="submit" class="w-full btn-primary">
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection