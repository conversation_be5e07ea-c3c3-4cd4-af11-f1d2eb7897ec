@extends('layouts.app')
@section('content')
    <h1>Blog</h1>
    @foreach($blogs as $blog)
        <h2><a href="{{ route('blog.show', $blog->slug) }}">{{ $blog->title }}</a></h2>
        <p>{{ Str::limit($blog->content, 150) }}</p>
    @endforeach
    {{ $blogs->links() }}
@endsection

// resources/views/blog/show.blade.php
@extends('layouts.app')
@section('content')
    <h1>{{ $blog->title }}</h1>
    <p>{{ $blog->content }}</p>
@endsection

// resources/views/admin/blog/create.blade.php
@extends('layouts.app')
@section('content')
    <h1>Add New Blog</h1>
    <form method="POST" action="{{ route('blog.store') }}">
        @csrf
        <label>Title</label>
        <input type="text" name="title" required>
        <label>Content</label>
        <textarea name="content" rows="6" required></textarea>
        <button type="submit">Publish</button>
    </form>
@endsection