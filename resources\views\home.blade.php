@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 text-white overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-20"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
                Discover Amazing
                <span class="text-yellow-300">Deals</span> &
                <span class="text-yellow-300">Coupons</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto animate-slide-up">
                Save money with verified coupon codes, exclusive deals, and lifestyle content from top brands worldwide.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
                <a href="{{ route('coupons.index') }}"
                   class="btn-primary text-lg px-8 py-4 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold">
                    Browse Coupons
                </a>
                <a href="{{ route('stores.index') }}"
                   class="btn-outline text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-blue-600">
                    Explore Stores
                </a>
            </div>
        </div>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-20 h-20 bg-yellow-400 rounded-full opacity-20 animate-bounce-gentle"></div>
    <div class="absolute bottom-20 right-10 w-16 h-16 bg-pink-400 rounded-full opacity-20 animate-bounce-gentle" style="animation-delay: 1s;"></div>
    <div class="absolute top-1/2 left-1/4 w-12 h-12 bg-green-400 rounded-full opacity-20 animate-bounce-gentle" style="animation-delay: 2s;"></div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div class="space-y-2">
                <div class="text-3xl md:text-4xl font-bold text-blue-600">500+</div>
                <div class="text-gray-600">Active Coupons</div>
            </div>
            <div class="space-y-2">
                <div class="text-3xl md:text-4xl font-bold text-green-600">200+</div>
                <div class="text-gray-600">Partner Stores</div>
            </div>
            <div class="space-y-2">
                <div class="text-3xl md:text-4xl font-bold text-purple-600">$2M+</div>
                <div class="text-gray-600">Money Saved</div>
            </div>
            <div class="space-y-2">
                <div class="text-3xl md:text-4xl font-bold text-orange-600">50K+</div>
                <div class="text-gray-600">Happy Users</div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Coupons -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                🔥 Hot Deals & Coupons
            </h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Don't miss out on these amazing deals! Limited time offers from your favorite brands.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            @forelse($featuredCoupons ?? [] as $coupon)
                @include('components.coupon-card', ['coupon' => $coupon])
            @empty
                <!-- Demo Coupon Cards -->
                <div class="card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <span class="text-red-600 font-bold text-lg">A</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Amazon</h3>
                                    <p class="text-sm text-gray-500">Electronics & More</p>
                                </div>
                            </div>
                            <span class="badge-danger">50% OFF</span>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">Up to 50% Off Electronics</h4>
                        <p class="text-gray-600 text-sm mb-4">Save big on laptops, smartphones, and gadgets. Limited time offer!</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Expires: Dec 31, 2024</span>
                            <button class="btn-primary text-sm">Get Deal</button>
                        </div>
                    </div>
                </div>

                <div class="card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span class="text-blue-600 font-bold text-lg">N</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Nike</h3>
                                    <p class="text-sm text-gray-500">Sports & Fashion</p>
                                </div>
                            </div>
                            <span class="badge-success">30% OFF</span>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">30% Off Athletic Wear</h4>
                        <p class="text-gray-600 text-sm mb-4">Get the latest Nike shoes and apparel at unbeatable prices.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Code: NIKE30</span>
                            <button class="btn-primary text-sm">Copy Code</button>
                        </div>
                    </div>
                </div>

                <div class="card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <span class="text-green-600 font-bold text-lg">S</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Starbucks</h3>
                                    <p class="text-sm text-gray-500">Food & Beverages</p>
                                </div>
                            </div>
                            <span class="badge-warning">BOGO</span>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">Buy One Get One Free</h4>
                        <p class="text-gray-600 text-sm mb-4">Enjoy your favorite drinks with a friend. Valid on all beverages.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Valid Today</span>
                            <button class="btn-primary text-sm">Get Offer</button>
                        </div>
                    </div>
                </div>
            @endforelse
        </div>

        <div class="text-center">
            <a href="{{ route('coupons.index') }}"
               class="btn-primary text-lg px-8 py-3">
                View All Coupons
            </a>
        </div>
    </div>
</section>

<!-- Popular Stores -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                🏪 Popular Stores
            </h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Shop from your favorite brands and discover new ones with exclusive deals.
            </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6 mb-8">
            @forelse($popularStores ?? [] as $store)
                <div class="card-hover text-center">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-gray-100 rounded-lg mx-auto mb-3 flex items-center justify-center">
                            <span class="text-2xl font-bold text-gray-600">{{ substr($store->name, 0, 1) }}</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-1">{{ $store->name }}</h3>
                        <p class="text-sm text-gray-500">{{ $store->activeCoupons->count() }} deals</p>
                    </div>
                </div>
            @empty
                <!-- Demo Store Cards -->
                <div class="card-hover text-center">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-red-100 rounded-lg mx-auto mb-3 flex items-center justify-center">
                            <span class="text-2xl font-bold text-red-600">A</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-1">Amazon</h3>
                        <p class="text-sm text-gray-500">25 deals</p>
                    </div>
                </div>

                <div class="card-hover text-center">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-blue-100 rounded-lg mx-auto mb-3 flex items-center justify-center">
                            <span class="text-2xl font-bold text-blue-600">N</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-1">Nike</h3>
                        <p class="text-sm text-gray-500">18 deals</p>
                    </div>
                </div>

                <div class="card-hover text-center">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-yellow-100 rounded-lg mx-auto mb-3 flex items-center justify-center">
                            <span class="text-2xl font-bold text-yellow-600">M</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-1">McDonald's</h3>
                        <p class="text-sm text-gray-500">12 deals</p>
                    </div>
                </div>

                <div class="card-hover text-center">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-purple-100 rounded-lg mx-auto mb-3 flex items-center justify-center">
                            <span class="text-2xl font-bold text-purple-600">A</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-1">Apple</h3>
                        <p class="text-sm text-gray-500">8 deals</p>
                    </div>
                </div>

                <div class="card-hover text-center">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-green-100 rounded-lg mx-auto mb-3 flex items-center justify-center">
                            <span class="text-2xl font-bold text-green-600">S</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-1">Starbucks</h3>
                        <p class="text-sm text-gray-500">15 deals</p>
                    </div>
                </div>

                <div class="card-hover text-center">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-pink-100 rounded-lg mx-auto mb-3 flex items-center justify-center">
                            <span class="text-2xl font-bold text-pink-600">U</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-1">Uber</h3>
                        <p class="text-sm text-gray-500">10 deals</p>
                    </div>
                </div>
            @endforelse
        </div>

        <div class="text-center">
            <a href="{{ route('stores.index') }}"
               class="btn-outline text-lg px-8 py-3">
                View All Stores
            </a>
        </div>
    </div>
</section>

<!-- Latest Blog Posts -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                📝 Latest Articles
            </h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Stay updated with the latest lifestyle tips, money-saving guides, and product reviews.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            @forelse($latestBlogs ?? [] as $blog)
                <article class="card-hover">
                    <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-xl">
                        @if($blog->featured_image)
                            <img src="{{ asset('storage/' . $blog->featured_image) }}"
                                 alt="{{ $blog->title }}"
                                 class="w-full h-48 object-cover rounded-t-xl">
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 rounded-t-xl flex items-center justify-center">
                                <span class="text-white text-4xl font-bold">{{ substr($blog->title, 0, 1) }}</span>
                            </div>
                        @endif
                    </div>
                    <div class="p-6">
                        <div class="flex items-center space-x-2 mb-3">
                            @if($blog->primaryCategory)
                                <span class="badge-primary">{{ $blog->primaryCategory->name }}</span>
                            @endif
                            <span class="text-sm text-gray-500">{{ $blog->published_at->format('M j, Y') }}</span>
                        </div>
                        <h3 class="font-bold text-xl text-gray-900 mb-2 line-clamp-2">{{ $blog->title }}</h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">{{ $blog->excerpt }}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                @if($blog->author)
                                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                                        <span class="text-white text-xs font-medium">{{ substr($blog->author->name, 0, 1) }}</span>
                                    </div>
                                    <span class="text-sm text-gray-500">{{ $blog->author->name }}</span>
                                @endif
                            </div>
                            <a href="{{ route('blog.show', $blog->slug) }}"
                               class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                Read More →
                            </a>
                        </div>
                    </div>
                </article>
            @empty
                <!-- Demo Blog Cards -->
                <article class="card-hover">
                    <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 rounded-t-xl flex items-center justify-center">
                        <span class="text-white text-4xl font-bold">💰</span>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center space-x-2 mb-3">
                            <span class="badge-primary">Money Saving</span>
                            <span class="text-sm text-gray-500">Dec 15, 2024</span>
                        </div>
                        <h3 class="font-bold text-xl text-gray-900 mb-2">10 Smart Ways to Save Money on Online Shopping</h3>
                        <p class="text-gray-600 mb-4">Discover proven strategies to maximize your savings when shopping online. From cashback apps to price comparison tools...</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-medium">J</span>
                                </div>
                                <span class="text-sm text-gray-500">John Doe</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                Read More →
                            </a>
                        </div>
                    </div>
                </article>

                <article class="card-hover">
                    <div class="w-full h-48 bg-gradient-to-br from-green-400 to-blue-500 rounded-t-xl flex items-center justify-center">
                        <span class="text-white text-4xl font-bold">🛍️</span>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center space-x-2 mb-3">
                            <span class="badge-success">Shopping Guide</span>
                            <span class="text-sm text-gray-500">Dec 12, 2024</span>
                        </div>
                        <h3 class="font-bold text-xl text-gray-900 mb-2">Best Black Friday Deals 2024: Complete Guide</h3>
                        <p class="text-gray-600 mb-4">Don't miss out on the biggest shopping event of the year. Here are the best deals and how to find them...</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-medium">S</span>
                                </div>
                                <span class="text-sm text-gray-500">Sarah Smith</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                Read More →
                            </a>
                        </div>
                    </div>
                </article>

                <article class="card-hover">
                    <div class="w-full h-48 bg-gradient-to-br from-purple-400 to-pink-500 rounded-t-xl flex items-center justify-center">
                        <span class="text-white text-4xl font-bold">🏠</span>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center space-x-2 mb-3">
                            <span class="badge-warning">Lifestyle</span>
                            <span class="text-sm text-gray-500">Dec 10, 2024</span>
                        </div>
                        <h3 class="font-bold text-xl text-gray-900 mb-2">Transform Your Home on a Budget: 15 DIY Ideas</h3>
                        <p class="text-gray-600 mb-4">Create a beautiful living space without breaking the bank. These budget-friendly DIY projects will inspire you...</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-medium">M</span>
                                </div>
                                <span class="text-sm text-gray-500">Mike Johnson</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                Read More →
                            </a>
                        </div>
                    </div>
                </article>
            @endforelse
        </div>

        <div class="text-center">
            <a href="{{ route('blog.index') }}"
               class="btn-outline text-lg px-8 py-3">
                Read More Articles
            </a>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            Never Miss a Deal Again! 📧
        </h2>
        <p class="text-xl mb-8 text-blue-100">
            Subscribe to our newsletter and get the best deals delivered straight to your inbox.
        </p>
        <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input type="email"
                   placeholder="Enter your email address"
                   class="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-yellow-400">
            <button type="submit"
                    class="btn-primary bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold px-8 py-3">
                Subscribe
            </button>
        </form>
        <p class="text-sm text-blue-200 mt-4">
            Join 50,000+ subscribers. No spam, unsubscribe anytime.
        </p>
    </div>
</section>
@endsection