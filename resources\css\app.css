@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply font-sans antialiased;
    }
}

@layer components {
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    }

    .btn-primary {
        @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
    }

    .btn-secondary {
        @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
    }

    .btn-success {
        @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
    }

    .btn-danger {
        @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
    }

    .btn-outline {
        @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500;
    }

    .card {
        @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
    }

    .card-hover {
        @apply card transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
    }

    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply badge bg-blue-100 text-blue-800;
    }

    .badge-success {
        @apply badge bg-green-100 text-green-800;
    }

    .badge-warning {
        @apply badge bg-yellow-100 text-yellow-800;
    }

    .badge-danger {
        @apply badge bg-red-100 text-red-800;
    }

    .badge-gray {
        @apply badge bg-gray-100 text-gray-800;
    }

    .input {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    }

    .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .gradient-text {
        @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
    }
}
