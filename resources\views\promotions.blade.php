@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
    <!-- Exclusive Header -->
    <section class="relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
            <div class="animate-pulse mb-4">
                <span class="inline-flex items-center px-4 py-2 bg-yellow-400 text-black rounded-full text-sm font-bold">
                    🔥 EXCLUSIVE ACCESS
                </span>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Secret Promotions
            </h1>
            <p class="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto">
                You've discovered our hidden treasure! These exclusive deals are only available to visitors who find this special page.
            </p>
            <div class="flex items-center justify-center space-x-2 text-yellow-400">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-sm font-medium">Limited Time • VIP Access Only</span>
            </div>
        </div>
    </section>

    <!-- Exclusive Coupons -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-white mb-4">💎 Exclusive Deals</h2>
                <p class="text-xl text-gray-300">These premium offers are not available anywhere else</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @forelse($exclusiveCoupons ?? [] as $coupon)
                    <div class="relative group">
                        <!-- Exclusive Badge -->
                        <div class="absolute -top-3 -right-3 z-10">
                            <span class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs font-bold rounded-full">
                                EXCLUSIVE
                            </span>
                        </div>
                        
                        <!-- Coupon Card with Glow Effect -->
                        <div class="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 hover:bg-white/20 transition-all duration-300 group-hover:scale-105 group-hover:shadow-2xl group-hover:shadow-purple-500/25">
                            <div class="flex items-center mb-4">
                                @if($coupon->store->logo ?? false)
                                    <img src="{{ asset('storage/' . $coupon->store->logo) }}" 
                                         alt="{{ $coupon->store->name }}" 
                                         class="w-12 h-12 rounded-lg object-cover mr-3">
                                @else
                                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                                        <span class="text-white font-bold">{{ substr($coupon->store->name ?? 'S', 0, 1) }}</span>
                                    </div>
                                @endif
                                <div>
                                    <h3 class="font-bold text-white">{{ $coupon->store->name ?? 'Premium Store' }}</h3>
                                    <p class="text-gray-300 text-sm">{{ $coupon->category->name ?? 'Exclusive' }}</p>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full text-lg font-bold mb-3">
                                    {{ $coupon->discount_display ?? '70% OFF' }}
                                </div>
                                <h4 class="text-xl font-bold text-white mb-2">{{ $coupon->title ?? 'Exclusive VIP Discount' }}</h4>
                                <p class="text-gray-300 text-sm">{{ $coupon->description ?? 'Special offer available only through this exclusive access page.' }}</p>
                            </div>

                            @if($coupon->code ?? false)
                                <div class="bg-black/30 rounded-lg p-3 mb-4">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-300 text-sm">Exclusive Code:</span>
                                        <div class="flex items-center space-x-2">
                                            <code class="bg-white/20 px-2 py-1 rounded text-white font-mono text-sm">{{ $coupon->code }}</code>
                                            <button class="text-yellow-400 text-sm hover:text-yellow-300" onclick="copyCode('{{ $coupon->code }}')">Copy</button>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <div class="flex space-x-2">
                                <a href="{{ route('coupons.visit', $coupon->slug ?? 'exclusive') }}" 
                                   target="_blank"
                                   class="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all duration-300">
                                    Get Exclusive Deal
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <!-- Demo Exclusive Coupons -->
                    @php
                        $exclusiveDeals = [
                            ['store' => 'Premium Electronics', 'discount' => '70% OFF', 'title' => 'VIP Electronics Sale', 'code' => 'VIP70', 'category' => 'Electronics'],
                            ['store' => 'Luxury Fashion', 'discount' => '60% OFF', 'title' => 'Exclusive Designer Collection', 'code' => 'LUXURY60', 'category' => 'Fashion'],
                            ['store' => 'Elite Travel', 'discount' => '$500 OFF', 'title' => 'Secret Travel Deals', 'code' => 'SECRET500', 'category' => 'Travel'],
                            ['store' => 'Premium Home', 'discount' => '50% OFF', 'title' => 'VIP Home Makeover', 'code' => 'HOME50', 'category' => 'Home & Garden'],
                            ['store' => 'Exclusive Beauty', 'discount' => 'BOGO FREE', 'title' => 'Beauty VIP Access', 'code' => 'BEAUTYV', 'category' => 'Beauty'],
                            ['store' => 'Elite Fitness', 'discount' => '65% OFF', 'title' => 'Premium Fitness Gear', 'code' => 'FIT65', 'category' => 'Fitness'],
                        ];
                    @endphp

                    @foreach($exclusiveDeals as $deal)
                        <div class="relative group">
                            <div class="absolute -top-3 -right-3 z-10">
                                <span class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs font-bold rounded-full">
                                    EXCLUSIVE
                                </span>
                            </div>
                            
                            <div class="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 hover:bg-white/20 transition-all duration-300 group-hover:scale-105 group-hover:shadow-2xl group-hover:shadow-purple-500/25">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                                        <span class="text-white font-bold">{{ substr($deal['store'], 0, 1) }}</span>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-white">{{ $deal['store'] }}</h3>
                                        <p class="text-gray-300 text-sm">{{ $deal['category'] }}</p>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full text-lg font-bold mb-3">
                                        {{ $deal['discount'] }}
                                    </div>
                                    <h4 class="text-xl font-bold text-white mb-2">{{ $deal['title'] }}</h4>
                                    <p class="text-gray-300 text-sm">Special offer available only through this exclusive access page.</p>
                                </div>

                                <div class="bg-black/30 rounded-lg p-3 mb-4">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-300 text-sm">Exclusive Code:</span>
                                        <div class="flex items-center space-x-2">
                                            <code class="bg-white/20 px-2 py-1 rounded text-white font-mono text-sm">{{ $deal['code'] }}</code>
                                            <button class="text-yellow-400 text-sm hover:text-yellow-300" onclick="copyCode('{{ $deal['code'] }}')">Copy</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex space-x-2">
                                    <button class="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all duration-300">
                                        Get Exclusive Deal
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @endforelse
            </div>
        </div>
    </section>

    <!-- High Value Deals -->
    <section class="py-16 bg-black/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-white mb-4">🚀 High-Value Deals</h2>
                <p class="text-xl text-gray-300">Premium offers with maximum savings</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                @forelse($highValueDeals ?? [] as $deal)
                    <div class="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-green-400/30 rounded-xl p-8 hover:from-green-600/30 hover:to-blue-600/30 transition-all duration-300">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                @if($deal->store->logo ?? false)
                                    <img src="{{ asset('storage/' . $deal->store->logo) }}" 
                                         alt="{{ $deal->store->name }}" 
                                         class="w-16 h-16 rounded-lg object-cover mr-4">
                                @else
                                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center mr-4">
                                        <span class="text-white font-bold text-xl">{{ substr($deal->store->name ?? 'H', 0, 1) }}</span>
                                    </div>
                                @endif
                                <div>
                                    <h3 class="text-2xl font-bold text-white">{{ $deal->store->name ?? 'High Value Store' }}</h3>
                                    <p class="text-green-300">{{ $deal->category->name ?? 'Premium Category' }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold text-green-400">{{ $deal->discount_display ?? '$100 OFF' }}</div>
                                <div class="text-sm text-gray-300">High Value</div>
                            </div>
                        </div>

                        <h4 class="text-xl font-bold text-white mb-3">{{ $deal->title ?? 'Premium High-Value Deal' }}</h4>
                        <p class="text-gray-300 mb-6">{{ $deal->description ?? 'Exceptional savings on premium products. This high-value offer provides maximum bang for your buck.' }}</p>

                        <div class="flex items-center justify-between">
                            @if($deal->code ?? false)
                                <div class="flex items-center space-x-3">
                                    <code class="bg-white/20 px-4 py-2 rounded-lg text-white font-mono">{{ $deal->code }}</code>
                                    <button class="text-green-400 hover:text-green-300" onclick="copyCode('{{ $deal->code }}')">Copy</button>
                                </div>
                            @endif
                            <a href="{{ route('coupons.visit', $deal->slug ?? 'high-value') }}" 
                               target="_blank"
                               class="bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-300">
                                Claim Deal
                            </a>
                        </div>
                    </div>
                @empty
                    <!-- Demo High Value Deals -->
                    @php
                        $highValueDeals = [
                            ['store' => 'Tech Giant', 'discount' => '$200 OFF', 'title' => 'Laptop Mega Sale', 'code' => 'TECH200'],
                            ['store' => 'Fashion Elite', 'discount' => '80% OFF', 'title' => 'Designer Clearance', 'code' => 'CLEAR80'],
                            ['store' => 'Travel Pro', 'discount' => '$1000 OFF', 'title' => 'Luxury Vacation Package', 'code' => 'LUXURY1K'],
                            ['store' => 'Home Premium', 'discount' => '$500 OFF', 'title' => 'Furniture Warehouse Sale', 'code' => 'HOME500'],
                        ];
                    @endphp

                    @foreach($highValueDeals as $deal)
                        <div class="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-green-400/30 rounded-xl p-8 hover:from-green-600/30 hover:to-blue-600/30 transition-all duration-300">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center mr-4">
                                        <span class="text-white font-bold text-xl">{{ substr($deal['store'], 0, 1) }}</span>
                                    </div>
                                    <div>
                                        <h3 class="text-2xl font-bold text-white">{{ $deal['store'] }}</h3>
                                        <p class="text-green-300">Premium Category</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-3xl font-bold text-green-400">{{ $deal['discount'] }}</div>
                                    <div class="text-sm text-gray-300">High Value</div>
                                </div>
                            </div>

                            <h4 class="text-xl font-bold text-white mb-3">{{ $deal['title'] }}</h4>
                            <p class="text-gray-300 mb-6">Exceptional savings on premium products. This high-value offer provides maximum bang for your buck.</p>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <code class="bg-white/20 px-4 py-2 rounded-lg text-white font-mono">{{ $deal['code'] }}</code>
                                    <button class="text-green-400 hover:text-green-300" onclick="copyCode('{{ $deal['code'] }}')">Copy</button>
                                </div>
                                <button class="bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-300">
                                    Claim Deal
                                </button>
                            </div>
                        </div>
                    @endforeach
                @endforelse
            </div>
        </div>
    </section>

    <!-- VIP Notice -->
    <section class="py-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="bg-gradient-to-r from-yellow-400/20 to-orange-500/20 backdrop-blur-sm border border-yellow-400/30 rounded-xl p-8">
                <div class="text-6xl mb-4">🎉</div>
                <h2 class="text-2xl font-bold text-white mb-4">Congratulations, VIP Member!</h2>
                <p class="text-gray-300 mb-6">
                    You've unlocked exclusive access to our premium deals. These offers are not available through regular browsing 
                    and are reserved for our most valued visitors. Bookmark this page to access these deals anytime!
                </p>
                <div class="flex items-center justify-center space-x-4 text-sm text-yellow-400">
                    <span>🔒 Exclusive Access</span>
                    <span>•</span>
                    <span>💎 Premium Deals</span>
                    <span>•</span>
                    <span>⚡ Limited Time</span>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
function copyCode(code) {
    navigator.clipboard.writeText(code).then(() => {
        // Show success message
        const event = new CustomEvent('show-toast', {
            detail: { message: `Code "${code}" copied to clipboard!`, type: 'success' }
        });
        window.dispatchEvent(event);
    });
}

// Add some sparkle effects
document.addEventListener('DOMContentLoaded', function() {
    // Create floating particles
    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'fixed w-2 h-2 bg-yellow-400 rounded-full opacity-70 pointer-events-none z-0';
        particle.style.left = Math.random() * window.innerWidth + 'px';
        particle.style.top = window.innerHeight + 'px';
        particle.style.animation = 'float-up 8s linear infinite';
        document.body.appendChild(particle);
        
        setTimeout(() => {
            particle.remove();
        }, 8000);
    }
    
    // Create particles periodically
    setInterval(createParticle, 2000);
});

// Add CSS for floating animation
const style = document.createElement('style');
style.textContent = `
    @keyframes float-up {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 0;
        }
        10% {
            opacity: 0.7;
        }
        90% {
            opacity: 0.7;
        }
        100% {
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>
@endsection
